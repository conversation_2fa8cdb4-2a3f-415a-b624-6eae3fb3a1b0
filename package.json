{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint --fix ."}, "dependencies": {"@iconify-json/lucide": "^1.2.49", "@iconify-json/simple-icons": "^1.2.38", "@nuxt/content": "3.6.1", "@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.14.0", "@nuxt/image": "1.10.0", "@nuxt/scripts": "0.11.8", "@nuxt/test-utils": "3.19.1", "@nuxt/ui": "^3.1.3", "@unhead/vue": "^2.0.3", "nuxt": "^3.17.5"}, "devDependencies": {"@nuxt/eslint": "^1.4.1", "eslint": "^9.29.0", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}