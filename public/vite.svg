<svg width="410" height="404" viewBox="0 0 410 404" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1_2)">
<path d="M205 0L410 404H0L205 0Z" fill="#646CFF"/>
<path d="M205 0L410 404H0L205 0Z" fill="url(#paint0_linear_1_2)" fill-opacity="0.5"/>
</g>
<defs>
<filter id="filter0_d_1_2" x="0" y="0" width="410" height="404" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.392157 0 0 0 0 0.423529 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_2"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_2" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1_2" x1="205" y1="0" x2="205" y2="404" gradientUnits="userSpaceOnUse">
<stop stop-color="#C084FC"/>
<stop offset="1" stop-color="#F472B6"/>
</linearGradient>
</defs>
</svg>
