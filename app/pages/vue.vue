<script setup lang="ts">
import HelloWorld from '~/components/HelloWorld.vue'
</script>

<template>
  <div class="rounded-xl p-6 shadow-lg backdrop-blur-md" style="background: linear-gradient(90deg, rgba(96,165,250,0.4) 0%, rgba(192,132,252,0.4) 50%, rgba(244,114,182,0.4) 100%);">
    <div class="flex justify-center items-center space-x-4">
      <a href="https://vite.dev" target="_blank">
        <img src="/vite.svg" class="logo" alt="Vite logo">
      </a>
      <a href="https://vuejs.org/" target="_blank">
        <img src="/assets/vue.svg" class="logo vue" alt="Vue logo">
      </a>
    </div>
    <HelloWorld msg="Hello World" />
  </div>
</template>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
